import React, { useState } from 'react';
import { format, addDays, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isSameMonth, isToday, isSameDay, addMonths, subMonths } from 'date-fns';
import { eachDayOfInterval } from 'date-fns/eachDayOfInterval';
import { CaretDown, CaretLeft, CaretRight } from '@phosphor-icons/react';

interface BusinessDateFilterProps {
  onApply: (startDate: Date, endDate: Date) => void;
}

type DateRange = 'This Week' | 'Last Week' | 'Custom';

// Helper function to format date range for display
const formatDateRange = (startDate: Date, endDate: Date): string => {
  const startFormatted = format(startDate, 'd MMM');
  const endFormatted = format(endDate, 'd MMM');
  return `${startFormatted} - ${endFormatted}`;
};

const BusinessDateFilter: React.FC<BusinessDateFilterProps> = ({ onApply }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<DateRange>('This Week');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [tempSelectedDate, setTempSelectedDate] = useState<Date>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [showCalendar, setShowCalendar] = useState(false);

  const handleOptionSelect = (option: DateRange) => {
    setSelectedOption(option);
    if (option === 'This Week') {
      const now = new Date();
      const start = startOfMonth(now);
      const end = endOfMonth(now);
      onApply(start, end);
      setIsOpen(false);
    } else if (option === 'Last Week') {
      const now = new Date();
      const lastMonth = subDays(now, 30);
      const start = startOfMonth(lastMonth);
      const end = endOfMonth(lastMonth);
      onApply(start, end);
      setIsOpen(false);
    } else {
      setShowCalendar(true);
    }
  };

  const handleDateSelect = (date: Date) => {
    setTempSelectedDate(date);
  };

  const handleApply = () => {
    setSelectedDate(tempSelectedDate);
    const endDate = tempSelectedDate;
    const startDate = subDays(endDate, 6);
    onApply(startDate, endDate);
    setIsOpen(false);
  };

  const handleCancel = () => {
    setTempSelectedDate(selectedDate);
    setIsOpen(false);
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(prevMonth => subMonths(prevMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(prevMonth => addMonths(prevMonth, 1));
  };

  const generateCalendarDays = () => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    const firstWeekStart = startOfWeek(start);
    const lastWeekEnd = endOfWeek(end);

    return eachDayOfInterval({ start: firstWeekStart, end: lastWeekEnd });
  };

  const isDateInRange = (date: Date) => {
    if (selectedOption === 'Custom' && tempSelectedDate) {
      const endDate = tempSelectedDate;
      const startDate = subDays(endDate, 6);
      return date >= startDate && date <= endDate;
    }
    return false;
  };

  return (
    <div className="relative" style={{fontFamily: 'Poppins, sans-serif'}}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 text-[16px] text-[#2D3134] font-medium"
      >
        {selectedOption === 'Custom' && selectedDate
          ? formatDateRange(subDays(selectedDate, 6), selectedDate)
          : selectedOption
        }
        <CaretDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute left-0 sm:right-0 sm:left-auto top-full mt-2 w-[280px] bg-white rounded-lg shadow-lg z-50 border border-gray-100">
          <div className="p-4">
            <div className="space-y-2">
              {['This Week', 'Last Week', 'Custom'].map((option) => (
                <button
                  key={option}
                  onClick={() => handleOptionSelect(option as DateRange)}
                  className={`w-full text-left px-3 py-2 rounded ${
                    selectedOption === option
                      ? 'bg-green-600/10 !text-green-600'
                      : 'hover:bg-gray-100/20'
                  }`}
                >
                  {option}
                </button>
              ))}
            </div>

            {showCalendar && selectedOption === 'Custom' && (
              <>
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-4">
                    <button 
                      className="p-1 hover:bg-gray-100 rounded-full"
                      onClick={handlePreviousMonth}
                    >
                      <CaretLeft size={16} />
                    </button>
                    <span className="text-sm font-medium">
                      {format(currentMonth, 'MMMM yyyy')}
                    </span>
                    <button
                      className="p-1 hover:bg-gray-100 rounded-full"
                      onClick={handleNextMonth}
                    >
                      <CaretRight size={16} />
                    </button>
                  </div>
                  <div className="grid grid-cols-7 gap-1">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                      <div key={day} className="text-center text-xs text-gray-500 font-medium">
                        {day}
                      </div>
                    ))}
                    {generateCalendarDays().map((date, index) => {
                      const isSelected = isSameDay(date, tempSelectedDate);
                      const inRange = isDateInRange(date);
                      const isCurrentMonth = isSameMonth(date, currentMonth);

                      return (
                        <button
                          key={index}
                          onClick={() => handleDateSelect(date)}
                          className={`
                            p-2 text-sm rounded-full text-center
                            ${!isCurrentMonth ? 'text-gray-300' : ''}
                            ${isSelected ? 'bg-green-600 text-white' : ''}
                            ${inRange && !isSelected ? 'bg-green-600/10' : ''}
                          `}
                        >
                          {format(date, 'd')}
                        </button>
                      );
                    })}
                  </div>
                </div>
                <div className="mt-4 flex justify-end gap-2 pt-2">
                  <button
                    onClick={handleCancel}
                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleApply}
                    className="px-4 py-2 text-sm font-medium text-white bg-[#1C1B1F] rounded hover:bg-[#2C2B2F]"
                  >
                    Apply
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessDateFilter; 