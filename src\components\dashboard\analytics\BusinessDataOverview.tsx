"use client";
import React, { useState } from "react";
import Image from "next/image";
import { BusinessDataItem } from "./types";

interface BusinessDataOverviewProps {
  data: BusinessDataItem[];
}

const BusinessDataOverview: React.FC<BusinessDataOverviewProps> = ({ data }) => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [showTooltip, setShowTooltip] = useState<number | null>(null);

  const imageMap = [
    "/assets/images/analytics/frame5.png",
    "/assets/images/analytics/frame6.png",
    "/assets/images/analytics/frame7.png"
  ];

  const tooltipTexts = [
    "Window has the highest occupancy",
    "Has the highest sessions",
    "Has the highest cancellations"
  ];

  return (
    <div className="space-y-6" style={{fontFamily: 'Poppins, sans-serif'}}>
      {/* <div className="flex items-center justify-between mb-[35px]">
        <h3 className="text-lg font-semibold text-primary">Business Data Overview</h3>
        <button className="flex items-center gap-2 text-[16px] text-[#2D3134] font-medium transition-colors">
          This Week
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
            <path d="M3.5 5.25L7 8.75L10.5 5.25" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div> */}

      {data.map((item, index) => (
        <div
          key={index}
          className="p-3 sm:p-4 rounded-lg relative overflow-hidden max-h-[133px] transition-all duration-300 hover:shadow-lg"
          style={{ backgroundColor: item.color, fontFamily: 'Poppins, sans-serif' }}
          onMouseEnter={() => setHoveredCard(index)}
          onMouseLeave={() => setHoveredCard(null)}
        >
          {/* Content */}
          <div className="flex-1 flex flex-col justify-center items-start relative z-10 pr-3 sm:pr-4 py-2 sm:py-4">
            <div className="space-y-2 sm:space-y-3 w-full">
              {/* Title with Info Icon */}
              <div className="flex items-start gap-2">
                <p className="text-[14px] font-semibold text-[#2D3134] leading-5">
                  {item.title}
                </p>
              {/* Info Icon with Tooltip */}
              <div className="relative flex-shrink-0">
                <button
                  onMouseEnter={() => setShowTooltip(index)}
                  onMouseLeave={() => setShowTooltip(null)}
                  className="transition-all duration-200"
                >
                  {showTooltip === index ? (
                    // Filled SVG
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path d="M1 10C1 7.61305 1.94821 5.32387 3.63604 3.63604C5.32387 1.94821 7.61305 1 10 1C12.3869 1 14.6761 1.94821 16.364 3.63604C18.0518 5.32387 19 7.61305 19 10C19 12.3869 18.0518 14.6761 16.364 16.364C14.6761 18.0518 12.3869 19 10 19C7.61305 19 5.32387 18.0518 3.63604 16.364C1.94821 14.6761 1 12.3869 1 10Z" fill="#2D3134"/>
                      <path d="M10.625 9.16797C10.625 9.00221 10.5592 8.84324 10.4419 8.72603C10.3247 8.60882 10.1658 8.54297 10 8.54297C9.83424 8.54297 9.67527 8.60882 9.55806 8.72603C9.44085 8.84324 9.375 9.00221 9.375 9.16797V14.168C9.375 14.3337 9.44085 14.4927 9.55806 14.6099C9.67527 14.7271 9.83424 14.793 10 14.793C10.1658 14.793 10.3247 14.7271 10.4419 14.6099C10.5592 14.4927 10.625 14.3337 10.625 14.168V9.16797Z" fill="white"/>
                      <path d="M10.8307 6.66927C10.8307 6.89028 10.7429 7.10225 10.5867 7.25853C10.4304 7.41481 10.2184 7.5026 9.9974 7.5026C9.77638 7.5026 9.56442 7.41481 9.40814 7.25853C9.25186 7.10225 9.16406 6.89028 9.16406 6.66927C9.16406 6.44826 9.25186 6.2363 9.40814 6.08002C9.56442 5.92374 9.77638 5.83594 9.9974 5.83594C10.2184 5.83594 10.4304 5.92374 10.5867 6.08002C10.7429 6.2363 10.8307 6.44826 10.8307 6.66927Z" fill="white"/>
                    </svg>
                  ) : (
                    // Unfilled SVG
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path d="M10.631 9.16413C10.631 8.99758 10.5648 8.83784 10.4471 8.72007C10.3293 8.6023 10.1696 8.53613 10.003 8.53613C9.83644 8.53613 9.67671 8.6023 9.55894 8.72007C9.44116 8.83784 9.375 8.99758 9.375 9.16413V14.1881C9.375 14.3547 9.44116 14.5144 9.55894 14.6322C9.67671 14.7499 9.83644 14.8161 10.003 14.8161C10.1696 14.8161 10.3293 14.7499 10.4471 14.6322C10.5648 14.5144 10.631 14.3547 10.631 14.1881V9.16413Z" fill="#2D3134"/>
                      <path fillRule="evenodd" clipRule="evenodd" d="M10.0013 1C5.03007 1 1 5.03007 1 10.0013C1 14.9725 5.03007 19.0026 10.0013 19.0026C14.9725 19.0026 19.0026 14.9725 19.0026 10.0013C19.0026 5.03007 14.9725 1 10.0013 1ZM2.256 10.0013C2.256 7.94712 3.07202 5.97707 4.52454 4.52454C5.97707 3.07202 7.94712 2.256 10.0013 2.256C12.0555 2.256 14.0255 3.07202 15.4781 4.52454C16.9306 5.97707 17.7466 7.94712 17.7466 10.0013C17.7466 12.0555 16.9306 14.0255 15.4781 15.4781C14.0255 16.9306 12.0555 17.7466 10.0013 17.7466C7.94712 17.7466 5.97707 16.9306 4.52454 15.4781C3.07202 14.0255 2.256 12.0555 2.256 10.0013Z" fill="#2D3134"/>
                      <path d="M10.8387 6.65374C10.8387 6.87581 10.7505 7.08879 10.5935 7.24582C10.4364 7.40285 10.2235 7.49107 10.0014 7.49107C9.77932 7.49107 9.56634 7.40285 9.40931 7.24582C9.25228 7.08879 9.16406 6.87581 9.16406 6.65374C9.16406 6.43166 9.25228 6.21868 9.40931 6.06165C9.56634 5.90462 9.77932 5.81641 10.0014 5.81641C10.2235 5.81641 10.4364 5.90462 10.5935 6.06165C10.7505 6.21868 10.8387 6.43166 10.8387 6.65374Z" fill="#2D3134"/>
                    </svg>
                  )}
                </button>

                {/* Tooltip */}
                {showTooltip === index && (
                  <div className="absolute top-6 right-0 bg-white border border-gray-200 rounded-lg shadow-lg p-3 w-48 z-20">
                    <div className="text-sm text-gray-700">
                      {tooltipTexts[index]}
                    </div>
                    {/* Tooltip Arrow */}
                    <div className="absolute -top-1 right-2 w-2 h-2 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                  </div>
                )}
                </div>
              </div>

              {/* Value */}
              <div>
                <p
                  className="text-[20px] font-semibold leading-6"
                  style={{ color: item.textColor }}
                >
                  {item.value}
                </p>
              </div>

              {/* Subtitle */}
              <div>
                <p className="text-[12px] font-medium text-[#2D313480] leading-4">
                  {item.subtitle}
                </p>
              </div>
            </div>
          </div>

          {/* Background Image */}
          <div className="absolute right-0 bottom-0">
            <Image
              src={imageMap[index]}
              alt={item.title}
              width={item.imageWidth}
              height={item.imageHeight}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default BusinessDataOverview;
