"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  Chart as ChartJS,
  ArcElement,
  Toolt<PERSON>,
  Legend,
  ChartOptions,
} from "chart.js";
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Doughnut } from "react-chartjs-2";
import { PaymentOverviewData } from "./types";

import DateRangeFilter from "@/components/common/DateRangeFilter";

ChartJS.register(ArcElement, Tooltip, Legend, ChartDataLabels);

interface PaymentOverviewChartProps {
  data: PaymentOverviewData;
  onDateChange?: (startDate: Date, endDate: Date) => void;
}

const PaymentOverviewChart: React.FC<PaymentOverviewChartProps> = ({
  data,
  onDateChange,
}) => {

  const chartData = {
    labels: ["Received After Sessions", "Cancellation Fees", "Advance Payment"],
    datasets: [
      {
        data: [
          data.receivedAfterSessions.amount,
          data.cancellationFees.amount,
          data.advancePayment.amount,
        ],
        backgroundColor: ["#FBE9D0", "#FFC7CE", "#D8E5FF"],
        borderWidth: 0,
        cutout: "70%",
      },
    ],
  };

  const options: ChartOptions<"doughnut"> = {
    responsive: true,
    maintainAspectRatio: true,
    plugins: {
      legend: {
        display: false,
      },
      datalabels: {
        display: true,
        color: (context) => {
          const value = context.dataset.data[context.dataIndex];
          // Return different colors based on the segment
          switch(context.dataIndex) {
            case 0: return '#D8A155'; // Red for first segment
            case 1: return '#DD6877'; // Black for second segment
            case 2: return '#5B84D5'; // Green for third segment
            default: return 'black'; // Default to black
          }
        },
        font: {
          weight: 'bold',
          size: 14,
        },
        formatter: (value, context) => {
          const total = context.dataset.data.reduce((a: number, b: any) => a + (Number(b) || 0), 0);
          const percentage = ((value / total) * 100).toFixed(0);
          return `${percentage}%`;
        },
      },
      tooltip: {
        backgroundColor: "#2D3134",
        titleColor: "#fff",
        bodyColor: "#fff",
        borderColor: "#E6E6E6",
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function (context) {
            const label = context.label || "";
            const value = context.parsed;
            const percentage = context.dataset.data.reduce((a: number, b: any) => a + (Number(b) || 0), 0);
            const percent = ((value / percentage) * 100).toFixed(0);
            return `${label}: ₹${value} (${percent}%)`;
          },
        },
      },
    },
  };

  return (
    <>
      <div className="bg-white p-3 sm:p-5 rounded-lg shadow-sm h-[564px] w-full md:w-[358px] 2xl:w-[358px] flex flex-col" style={{ fontFamily: 'Poppins, sans-serif' }}>
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6">
          <h3 className="text-base sm:text-lg font-semibold text-primary mb-2 sm:mb-0">Payment Overview</h3>
          <DateRangeFilter
            onApply={(startDate, endDate) => {
              if (onDateChange) {
                onDateChange(startDate, endDate);
              }
            }}
          />
        </div>

        {/* Top Stats */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center gap-2">
            <div className="text-lg sm:text-[20px] font-semibold">₹{data.avgSessionFee}</div>
            <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">Avg/Session Fee</div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-lg sm:text-[20px] font-semibold">{data.noOfSessions}</div>
            <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">No.Of Session</div>
          </div>
        </div>

        {/* Chart Container */}
        <div className="flex flex-col items-center flex-1">
          {/* Chart */}
          <div className="h-[200px] w-[200px] sm:h-[250px] sm:w-[250px] relative mb-4">
            <Doughnut data={chartData} options={options} />
            {/* Center Text */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-sm sm:text-base font-bold text-primary">₹{data.totalPayment}</div>
              <div className="text-xs text-gray-500">Total Payment</div>
            </div>
          </div>

          {/* Legend */}
          <div className="space-y-4 w-full max-w-xs pt-4">
            {/* First Legend Item */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[#FBE9D0]"></div>
                <span className="text-sm text-gray-700">Received After Sessions</span>
              </div>
              <span className="text-sm font-medium">{data.receivedAfterSessions.percentage}%</span>
            </div>
            <div className="border-t border-dashed border-gray-100"></div>

            {/* Second Legend Item */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[#FFCACC]"></div>
                <span className="text-sm text-gray-700">Cancellation Dues Collected</span>
              </div>
              <span className="text-sm font-medium">{data.cancellationFees.percentage}%</span>
            </div>
            <div className="border-t border-dashed border-gray-100"></div>

            {/* Third Legend Item */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[#A9C6FF]"></div>
                <span className="text-sm text-gray-700">Advance Payment</span>
              </div>
              <span className="text-sm font-medium">{data.advancePayment.percentage}%</span>
            </div>
          </div>
        </div>
      </div>


    </>
  );
};

export default PaymentOverviewChart;
