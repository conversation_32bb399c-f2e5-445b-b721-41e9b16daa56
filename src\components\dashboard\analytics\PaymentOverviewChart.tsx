"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  ChartOptions,
} from "chart.js";
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Doughnut } from "react-chartjs-2";
import { PaymentOverviewData } from "./types";
import { CaretDown, Check } from "@phosphor-icons/react";
import DateRangeFilter from "@/components/common/DateRangeFilter";
import CancellationChart from "../CancellationChart";

ChartJS.register(ArcElement, Tooltip, Legend, ChartDataLabels);

interface CancellationData {
  cancelled_sessions: Array<{
    name: string;
    session: number;
  }>;
  total_cancelled_sessions: number;
  collected_fees: number;
  pending_fees: number;
}

interface PaymentOverviewChartProps {
  data: PaymentOverviewData;
  cancellationData?: CancellationData;
  onPaymentDateChange?: (startDate: Date, endDate: Date) => void;
  onCancellationDateChange?: (startDate: Date, endDate: Date) => void;
}

const PaymentOverviewChart: React.FC<PaymentOverviewChartProps> = ({
  data,
  cancellationData,
  onPaymentDateChange,
  onCancellationDateChange,
}) => {
  const [selectedView, setSelectedView] = useState<'payment' | 'cancellation'>('payment');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const chartData = {
    labels: ["Received After Sessions", "Cancellation Fees", "Advance Payment"],
    datasets: [
      {
        data: [
          data.receivedAfterSessions.amount,
          data.cancellationFees.amount,
          data.advancePayment.amount,
        ],
        backgroundColor: ["#FBE9D0", "#FFC7CE", "#D8E5FF"],
        borderWidth: 0,
        cutout: "70%",
      },
    ],
  };

  const options: ChartOptions<"doughnut"> = {
    responsive: true,
    maintainAspectRatio: true,
    plugins: {
      legend: {
        display: false,
      },
      datalabels: {
        display: true,
        color: (context) => {
          const value = context.dataset.data[context.dataIndex];
          // Return different colors based on the segment
          switch(context.dataIndex) {
            case 0: return '#D8A155'; // Red for first segment
            case 1: return '#DD6877'; // Black for second segment
            case 2: return '#5B84D5'; // Green for third segment
            default: return 'black'; // Default to black
          }
        },
        font: {
          weight: 'bold',
          size: 14,
        },
        formatter: (value, context) => {
          const total = context.dataset.data.reduce((a: number, b: any) => a + (Number(b) || 0), 0);
          const percentage = ((value / total) * 100).toFixed(0);
          return `${percentage}%`;
        },
      },
      tooltip: {
        backgroundColor: "#2D3134",
        titleColor: "#fff",
        bodyColor: "#fff",
        borderColor: "#E6E6E6",
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function (context) {
            const label = context.label || "";
            const value = context.parsed;
            const percentage = context.dataset.data.reduce((a: number, b: any) => a + (Number(b) || 0), 0);
            const percent = ((value / percentage) * 100).toFixed(0);
            return `${label}: ₹${value} (${percent}%)`;
          },
        },
      },
    },
  };

  return (
    <>
      <div className="bg-white p-3 sm:p-5 rounded-lg shadow-sm h-[564px] w-full md:w-[358px] 2xl:w-[358px] flex flex-col" style={{ fontFamily: 'Poppins, sans-serif' }}>
        {/* Header with Toggle */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6">
          <div className="flex items-center gap-3 mb-2 sm:mb-0">
            <h3 className="text-base sm:text-lg font-semibold text-primary">
              {selectedView === 'payment' ? 'Payment Overview' : 'Cancellation'}
            </h3>
            <div className="relative" ref={dropdownRef}>
              <button
                className="flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                <CaretDown
                  size={16}
                  className={`transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
                />
              </button>
              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                  <div className="py-1">
                    <button
                      className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 ${
                        selectedView === 'payment' ? 'text-green-600 bg-green-50' : 'text-gray-700'
                      }`}
                      onClick={() => {
                        setSelectedView('payment');
                        setIsDropdownOpen(false);
                      }}
                    >
                      {selectedView === 'payment' && <Check size={16} />}
                      <span className={selectedView === 'payment' ? 'ml-0' : 'ml-6'}>Payment Overview</span>
                    </button>
                    <button
                      className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 ${
                        selectedView === 'cancellation' ? 'text-green-600 bg-green-50' : 'text-gray-700'
                      }`}
                      onClick={() => {
                        setSelectedView('cancellation');
                        setIsDropdownOpen(false);
                      }}
                    >
                      {selectedView === 'cancellation' && <Check size={16} />}
                      <span className={selectedView === 'cancellation' ? 'ml-0' : 'ml-6'}>Cancellation Overview</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          <DateRangeFilter
            onApply={(startDate, endDate) => {
              if (selectedView === 'payment' && onPaymentDateChange) {
                onPaymentDateChange(startDate, endDate);
              } else if (selectedView === 'cancellation' && onCancellationDateChange) {
                onCancellationDateChange(startDate, endDate);
              }
            }}
          />
        </div>

        {/* Content based on selected view */}
        {selectedView === 'payment' ? (
          <>
            {/* Top Stats */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
              <div className="flex items-center gap-2">
                <div className="text-lg sm:text-[20px] font-semibold">₹{data.avgSessionFee}</div>
                <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">Avg/Session Fee</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-lg sm:text-[20px] font-semibold">{data.noOfSessions}</div>
                <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">No.Of Session</div>
              </div>
            </div>

            {/* Chart Container */}
            <div className="flex flex-col items-center flex-1">
              {/* Chart */}
              <div className="h-[200px] w-[200px] sm:h-[250px] sm:w-[250px] relative mb-4">
                <Doughnut data={chartData} options={options} />
                {/* Center Text */}
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="text-sm sm:text-base font-bold text-primary">₹{data.totalPayment}</div>
                  <div className="text-xs text-gray-500">Total Payment</div>
                </div>
              </div>

              {/* Legend */}
              <div className="space-y-4 w-full max-w-xs pt-4">
                {/* First Legend Item */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#FBE9D0]"></div>
                    <span className="text-sm text-gray-700">Received After Sessions</span>
                  </div>
                  <span className="text-sm font-medium">{data.receivedAfterSessions.percentage}%</span>
                </div>
                <div className="border-t border-dashed border-gray-100"></div>

                {/* Second Legend Item */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#FFCACC]"></div>
                    <span className="text-sm text-gray-700">Cancellation Dues Collected</span>
                  </div>
                  <span className="text-sm font-medium">{data.cancellationFees.percentage}%</span>
                </div>
                <div className="border-t border-dashed border-gray-100"></div>

                {/* Third Legend Item */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#A9C6FF]"></div>
                    <span className="text-sm text-gray-700">Advance Payment</span>
                  </div>
                  <span className="text-sm font-medium">{data.advancePayment.percentage}%</span>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Cancellation Overview Content */}
            {cancellationData && cancellationData.cancelled_sessions && cancellationData.cancelled_sessions.length > 0 ? (
              <>
                {/* Top Stats for Cancellation */}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
                  <div className="flex items-center gap-2">
                    <div className="text-lg sm:text-[20px] font-semibold">₹{cancellationData.collected_fees}</div>
                    <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">Collected Fees</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-lg sm:text-[20px] font-semibold">₹{cancellationData.pending_fees}</div>
                    <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">Pending Fees</div>
                  </div>
                </div>

                {/* Cancellation Chart */}
                <div className="flex flex-col items-center flex-1">
                  <div className="h-[200px] w-[200px] sm:h-[250px] sm:w-[250px] relative mb-4">
                    <CancellationChart cancellationData={cancellationData} />
                  </div>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center flex-1">
                <div className="text-center">
                  <p className="text-gray-500 text-sm">No cancellation data available</p>
                </div>
              </div>
            )}
          </>
        )}
      </div>


    </>
  );
};

export default PaymentOverviewChart;
